# Cross-Document Integration: Prosedur dan Instruksi Kerja ↔ Evidence Mapping Klausul

## Overview

Sistem integrasi cross-document memungkinkan progress dari section **Prosedur dan Instruksi Kerja (doc-2)** secara otomatis terintegrasi ke section **Evidence Mapping Klausul (doc-3)**. Ketika Admin Super atau Admin Biasa membuat section/subsection di doc-2, dan <PERSON> melakukan aktivitas (upload file/isi form), progress akan otomatis muncul di Evidence Mapping Klausul.

## Cara Kerja

### 1. Mapping Configuration
- Admin dapat mengkonfigurasi mapping antara section di doc-2 dengan section di doc-3
- Default mappings sudah tersedia:
  - `manual-smap` → `doc-3-section-1` (Klausul ISO 37001)
  - `struktur-organisasi` → `doc-3-section-2` (Konteks Organisasi)
  - `kebijakan-anti-suap` → `doc-3-section-3` (Kepemimpinan)
  - `formulir-risk-assessment` → `doc-3-section-4` (Perencanaan)

### 2. Auto-Sync Process
1. **Admin/Super Admin** membuat section dan subsection di doc-2
2. **Scoopers** melakukan aktivitas:
   - Upload file ke subsection
   - Mengisi form (untuk subsection tipe form)
3. **Sistem otomatis**:
   - Mendeteksi aktivitas di doc-2
   - Menghitung progress di doc-2
   - Menyinkronkan progress ke doc-3 berdasarkan mapping
   - Menampilkan integrated progress di Evidence Mapping

### 3. Progress Calculation
- **Source Progress**: Progress asli dari doc-2 (100% = semua subsection selesai)
- **Target Progress**: Progress yang muncul di doc-3 (80% dari source progress)
- **Combined Progress**: Gabungan weighted average (60% source + 40% target)

## Komponen Utama

### 1. `useCrossDocumentIntegration` Hook
```typescript
const { 
  integratedProgress,
  mappings,
  addMapping,
  removeMapping,
  toggleMapping,
  syncProgress 
} = useCrossDocumentIntegration();
```

**Fungsi:**
- Mengelola mapping configuration
- Menghitung integrated progress
- Menyinkronkan progress antar dokumen

### 2. `useAutoProgressSync` Hook
```typescript
const { triggerSync } = useAutoProgressSync();

// Trigger sync saat file diupload
triggerSync('doc-2', sectionId, user.name);
```

**Fungsi:**
- Memicu sinkronisasi otomatis
- Dipanggil saat ada aktivitas di doc-2

### 3. `IntegratedProgressCard` Component
Menampilkan progress terintegrasi dengan:
- Source progress (doc-2)
- Target progress (doc-3)
- Combined progress
- Detail mapping
- Status indicator

### 4. `CrossDocumentMappingConfig` Component
Interface untuk admin mengelola mapping:
- Tambah mapping baru
- Edit mapping existing
- Toggle aktif/nonaktif mapping
- Hapus mapping

## User Roles & Permissions

### Admin Super & Admin Biasa
- ✅ Membuat section/subsection di doc-2
- ✅ Mengkonfigurasi mapping cross-document
- ✅ Melihat integrated progress di doc-3
- ✅ Upload file dan isi form

### Scoopers
- ✅ Upload file ke subsection di doc-2
- ✅ Mengisi form di subsection doc-2
- ✅ Melihat integrated progress di doc-3
- ❌ Tidak bisa konfigurasi mapping

## Implementasi

### 1. Di Prosedur dan Instruksi Kerja (doc-2)
```typescript
// Import hook
import { useAutoProgressSync } from '@/hooks/useCrossDocumentIntegration';

// Dalam component
const { triggerSync } = useAutoProgressSync();

// Trigger sync saat file berhasil diupload
const handleFileUploaded = (sectionId: string) => {
  if (user) {
    triggerSync('doc-2', sectionId, user.name);
  }
};
```

### 2. Di Evidence Mapping Klausul (doc-3)
```typescript
// Import hooks dan components
import { useCrossDocumentIntegration } from '@/hooks/useCrossDocumentIntegration';
import { IntegratedProgressList } from '@/components/ui/IntegratedProgressCard';

// Dalam component
const { integratedProgress, calculateIntegratedProgress } = useCrossDocumentIntegration();

// Auto-refresh progress
useEffect(() => {
  calculateIntegratedProgress();
  const interval = setInterval(calculateIntegratedProgress, 30000);
  return () => clearInterval(interval);
}, [calculateIntegratedProgress]);

// Render integrated progress
<IntegratedProgressList 
  progressList={integratedProgress}
  showDetails={true}
/>
```

## Data Storage

### 1. Cross-Document Mappings
```typescript
// Stored in localStorage: 'cross-document-mappings'
interface CrossDocumentMapping {
  sourceDocumentId: string;    // 'doc-2'
  sourceSectionId: string;     // 'manual-smap'
  targetDocumentId: string;    // 'doc-3'
  targetSectionId: string;     // 'doc-3-section-1'
  mappingType: 'one_to_one' | 'one_to_many' | 'many_to_one';
  isActive: boolean;
}
```

### 2. Progress Data
```typescript
// Integrated progress calculation
interface IntegratedProgress {
  sectionId: string;
  sectionTitle: string;
  sourceProgress: number;      // Progress dari doc-2
  targetProgress: number;      // Progress di doc-3
  combinedProgress: number;    // Gabungan weighted
  lastUpdated: string;
  mappings: CrossDocumentMapping[];
}
```

## Benefits

1. **Automatic Synchronization**: Progress otomatis tersinkronisasi tanpa manual input
2. **Real-time Updates**: Progress update setiap 30 detik
3. **Flexible Mapping**: Admin bisa konfigurasi mapping sesuai kebutuhan
4. **Visual Feedback**: Progress ditampilkan dengan jelas di Evidence Mapping
5. **Audit Trail**: Semua aktivitas tercatat dengan timestamp dan user

## Troubleshooting

### Progress Tidak Muncul
1. Cek apakah mapping sudah dikonfigurasi dan aktif
2. Pastikan ada aktivitas (file upload/form submit) di doc-2
3. Refresh halaman atau tunggu auto-refresh (30 detik)

### Mapping Tidak Berfungsi
1. Pastikan section ID sesuai dengan yang ada di sistem
2. Cek apakah mapping dalam status aktif
3. Verifikasi user role memiliki permission

### Performance Issues
1. Batasi jumlah mapping aktif
2. Gunakan mapping type yang sesuai kebutuhan
3. Monitor localStorage usage

## Future Enhancements

1. **Real-time WebSocket Updates**: Mengganti polling dengan WebSocket
2. **Advanced Mapping Rules**: Conditional mapping berdasarkan criteria tertentu
3. **Progress Analytics**: Dashboard analytics untuk progress tracking
4. **Notification System**: Notifikasi saat progress berubah
5. **Export/Import Mapping**: Backup dan restore mapping configuration
