'use client';

import { useState } from 'react';
import { 
  CogIcon, 
  LinkIcon, 
  PlusIcon, 
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { useCrossDocumentIntegration, CrossDocumentMapping } from '@/hooks/useCrossDocumentIntegration';

interface CrossDocumentMappingConfigProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CrossDocumentMappingConfig({ isOpen, onClose }: CrossDocumentMappingConfigProps) {
  const { mappings, addMapping, removeMapping, toggleMapping } = useCrossDocumentIntegration();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMapping, setNewMapping] = useState({
    sourceDocumentId: 'doc-2',
    sourceSectionId: '',
    targetDocumentId: 'doc-3',
    targetSectionId: '',
    mappingType: 'one_to_one' as 'one_to_one' | 'one_to_many' | 'many_to_one'
  });

  if (!isOpen) return null;

  const handleAddMapping = () => {
    if (newMapping.sourceSectionId && newMapping.targetSectionId) {
      addMapping(newMapping);
      setNewMapping({
        sourceDocumentId: 'doc-2',
        sourceSectionId: '',
        targetDocumentId: 'doc-3',
        targetSectionId: '',
        mappingType: 'one_to_one'
      });
      setShowAddForm(false);
    }
  };

  const handleRemoveMapping = (mapping: CrossDocumentMapping) => {
    removeMapping(
      mapping.sourceDocumentId,
      mapping.sourceSectionId,
      mapping.targetDocumentId,
      mapping.targetSectionId
    );
  };

  const handleToggleMapping = (mapping: CrossDocumentMapping) => {
    toggleMapping(
      mapping.sourceDocumentId,
      mapping.sourceSectionId,
      mapping.targetDocumentId,
      mapping.targetSectionId
    );
  };

  const getDocumentTitle = (docId: string) => {
    const titles: Record<string, string> = {
      'doc-1': 'Pedoman Peraturan Perusahaan',
      'doc-2': 'Prosedur dan Instruksi Kerja',
      'doc-3': 'Evidence Mapping Klausul',
      'doc-4': 'Fungsi Kepatuhan Anti Penyuapan'
    };
    return titles[docId] || docId;
  };

  const getMappingTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'one_to_one': 'Satu ke Satu',
      'one_to_many': 'Satu ke Banyak',
      'many_to_one': 'Banyak ke Satu'
    };
    return labels[type] || type;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CogIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="font-gotham-rounded text-xl font-bold text-gray-900">
                  Konfigurasi Mapping Cross-Document
                </h2>
                <p className="font-gotham text-sm text-gray-600">
                  Kelola integrasi antara Prosedur dan Instruksi Kerja dengan Evidence Mapping
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <XCircleIcon className="h-6 w-6 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Add New Mapping Button */}
          <div className="mb-6">
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Tambah Mapping Baru</span>
            </button>
          </div>

          {/* Add New Mapping Form */}
          {showAddForm && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900 mb-4">
                Tambah Mapping Baru
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
                    Source Section ID (doc-2)
                  </label>
                  <input
                    type="text"
                    value={newMapping.sourceSectionId}
                    onChange={(e) => setNewMapping({ ...newMapping, sourceSectionId: e.target.value })}
                    placeholder="e.g., manual-smap"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
                  />
                </div>
                <div>
                  <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
                    Target Section ID (doc-3)
                  </label>
                  <input
                    type="text"
                    value={newMapping.targetSectionId}
                    onChange={(e) => setNewMapping({ ...newMapping, targetSectionId: e.target.value })}
                    placeholder="e.g., doc-3-section-1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
                  />
                </div>
                <div>
                  <label className="block font-gotham text-sm font-medium text-gray-700 mb-2">
                    Mapping Type
                  </label>
                  <select
                    value={newMapping.mappingType}
                    onChange={(e) => setNewMapping({ ...newMapping, mappingType: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham"
                  >
                    <option value="one_to_one">Satu ke Satu</option>
                    <option value="one_to_many">Satu ke Banyak</option>
                    <option value="many_to_one">Banyak ke Satu</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-3 mt-4">
                <button
                  onClick={handleAddMapping}
                  disabled={!newMapping.sourceSectionId || !newMapping.targetSectionId}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium"
                >
                  Tambah Mapping
                </button>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors font-gotham font-medium"
                >
                  Batal
                </button>
              </div>
            </div>
          )}

          {/* Existing Mappings */}
          <div className="space-y-4">
            <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900">
              Mapping yang Ada ({mappings.length})
            </h3>
            
            {mappings.length === 0 ? (
              <div className="text-center py-8">
                <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="font-gotham text-gray-600">Belum ada mapping yang dikonfigurasi</p>
              </div>
            ) : (
              mappings.map((mapping, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    mapping.isActive 
                      ? 'border-green-200 bg-green-50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${
                        mapping.isActive ? 'bg-green-100' : 'bg-gray-100'
                      }`}>
                        <LinkIcon className={`h-5 w-5 ${
                          mapping.isActive ? 'text-green-600' : 'text-gray-500'
                        }`} />
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="text-center">
                          <p className="font-gotham text-sm font-medium text-gray-900">
                            {getDocumentTitle(mapping.sourceDocumentId)}
                          </p>
                          <p className="font-gotham text-xs text-gray-500">
                            {mapping.sourceSectionId}
                          </p>
                        </div>
                        
                        <ArrowRightIcon className="h-4 w-4 text-gray-400" />
                        
                        <div className="text-center">
                          <p className="font-gotham text-sm font-medium text-gray-900">
                            {getDocumentTitle(mapping.targetDocumentId)}
                          </p>
                          <p className="font-gotham text-xs text-gray-500">
                            {mapping.targetSectionId}
                          </p>
                        </div>
                      </div>
                      
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        mapping.mappingType === 'one_to_one' ? 'bg-blue-100 text-blue-700' :
                        mapping.mappingType === 'one_to_many' ? 'bg-purple-100 text-purple-700' :
                        'bg-orange-100 text-orange-700'
                      }`}>
                        {getMappingTypeLabel(mapping.mappingType)}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleToggleMapping(mapping)}
                        className={`p-2 rounded-lg transition-colors ${
                          mapping.isActive 
                            ? 'bg-green-100 text-green-600 hover:bg-green-200' 
                            : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                        }`}
                        title={mapping.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                      >
                        {mapping.isActive ? (
                          <CheckCircleIcon className="h-5 w-5" />
                        ) : (
                          <XCircleIcon className="h-5 w-5" />
                        )}
                      </button>
                      
                      <button
                        onClick={() => handleRemoveMapping(mapping)}
                        className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                        title="Hapus Mapping"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-gotham font-medium"
            >
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
