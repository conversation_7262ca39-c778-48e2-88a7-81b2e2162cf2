import { useState, useEffect, useCallback } from 'react';
import { useProgressStore } from '@/store/progress';
import { useSubsectionActions } from './useSubsectionActions';

export interface CrossDocumentMapping {
  sourceDocumentId: string;
  sourceSectionId: string;
  targetDocumentId: string;
  targetSectionId: string;
  mappingType: 'one_to_one' | 'one_to_many' | 'many_to_one';
  isActive: boolean;
}

export interface IntegratedProgress {
  sectionId: string;
  sectionTitle: string;
  sourceProgress: number;
  targetProgress: number;
  combinedProgress: number;
  lastUpdated: string;
  mappings: CrossDocumentMapping[];
}

// Default mappings between Prosedur dan <PERSON>ruk<PERSON> (doc-2) and Evidence Mapping (doc-3)
const DEFAULT_CROSS_DOCUMENT_MAPPINGS: CrossDocumentMapping[] = [
  {
    sourceDocumentId: 'doc-2',
    sourceSectionId: 'manual-smap',
    targetDocumentId: 'doc-3',
    targetSectionId: 'doc-3-section-1', // Klausul ISO 37001
    mappingType: 'one_to_one',
    isActive: true
  },
  {
    sourceDocumentId: 'doc-2',
    sourceSectionId: 'struktur-organisasi',
    targetDocumentId: 'doc-3',
    targetSectionId: 'doc-3-section-2', // Konteks Organisasi
    mappingType: 'one_to_one',
    isActive: true
  },
  {
    sourceDocumentId: 'doc-2',
    sourceSectionId: 'kebijakan-anti-suap',
    targetDocumentId: 'doc-3',
    targetSectionId: 'doc-3-section-3', // Kepemimpinan
    mappingType: 'one_to_one',
    isActive: true
  },
  {
    sourceDocumentId: 'doc-2',
    sourceSectionId: 'formulir-risk-assessment',
    targetDocumentId: 'doc-3',
    targetSectionId: 'doc-3-section-4', // Perencanaan
    mappingType: 'one_to_one',
    isActive: true
  }
];

export function useCrossDocumentIntegration() {
  const [mappings, setMappings] = useState<CrossDocumentMapping[]>(DEFAULT_CROSS_DOCUMENT_MAPPINGS);
  const [integratedProgress, setIntegratedProgress] = useState<IntegratedProgress[]>([]);
  const { getDocumentProgress, updateSectionProgress, addOrUpdateSection } = useProgressStore();
  const { getDocumentProgress: getSubsectionProgress } = useSubsectionActions('doc-2');
  const { getDocumentProgress: getEvidenceProgress } = useSubsectionActions('doc-3');

  // Load mappings from localStorage
  useEffect(() => {
    const savedMappings = localStorage.getItem('cross-document-mappings');
    if (savedMappings) {
      try {
        const parsed = JSON.parse(savedMappings);
        setMappings(parsed);
      } catch (error) {
        console.error('Error loading cross-document mappings:', error);
      }
    }
  }, []);

  // Save mappings to localStorage
  const saveMappings = useCallback((newMappings: CrossDocumentMapping[]) => {
    setMappings(newMappings);
    localStorage.setItem('cross-document-mappings', JSON.stringify(newMappings));
  }, []);

  // Add new mapping
  const addMapping = useCallback((mapping: Omit<CrossDocumentMapping, 'isActive'>) => {
    const newMapping: CrossDocumentMapping = {
      ...mapping,
      isActive: true
    };
    const updatedMappings = [...mappings, newMapping];
    saveMappings(updatedMappings);
  }, [mappings, saveMappings]);

  // Remove mapping
  const removeMapping = useCallback((sourceDocId: string, sourceSectionId: string, targetDocId: string, targetSectionId: string) => {
    const updatedMappings = mappings.filter(m => 
      !(m.sourceDocumentId === sourceDocId && 
        m.sourceSectionId === sourceSectionId && 
        m.targetDocumentId === targetDocId && 
        m.targetSectionId === targetSectionId)
    );
    saveMappings(updatedMappings);
  }, [mappings, saveMappings]);

  // Toggle mapping active status
  const toggleMapping = useCallback((sourceDocId: string, sourceSectionId: string, targetDocId: string, targetSectionId: string) => {
    const updatedMappings = mappings.map(m => {
      if (m.sourceDocumentId === sourceDocId && 
          m.sourceSectionId === sourceSectionId && 
          m.targetDocumentId === targetDocId && 
          m.targetSectionId === targetSectionId) {
        return { ...m, isActive: !m.isActive };
      }
      return m;
    });
    saveMappings(updatedMappings);
  }, [mappings, saveMappings]);

  // Calculate integrated progress
  const calculateIntegratedProgress = useCallback(() => {
    const activeMappings = mappings.filter(m => m.isActive);
    const progressData: IntegratedProgress[] = [];

    activeMappings.forEach(mapping => {
      const sourceDoc = getDocumentProgress(mapping.sourceDocumentId);
      const targetDoc = getDocumentProgress(mapping.targetDocumentId);
      
      const sourceSection = sourceDoc?.sections.find(s => s.id === mapping.sourceSectionId);
      const targetSection = targetDoc?.sections.find(s => s.id === mapping.targetSectionId);

      if (sourceSection && targetSection) {
        const sourceProgress = sourceSection.progressPercentage || 0;
        const targetProgress = targetSection.progressPercentage || 0;
        
        // Combined progress calculation (weighted average)
        const combinedProgress = Math.round((sourceProgress * 0.6) + (targetProgress * 0.4));

        progressData.push({
          sectionId: targetSection.id,
          sectionTitle: targetSection.title,
          sourceProgress,
          targetProgress,
          combinedProgress,
          lastUpdated: new Date().toISOString(),
          mappings: [mapping]
        });
      }
    });

    setIntegratedProgress(progressData);
    return progressData;
  }, [mappings, getDocumentProgress]);

  // Sync progress from source to target
  const syncProgress = useCallback((sourceDocId: string, sourceSectionId: string, updatedBy: string) => {
    const relevantMappings = mappings.filter(m => 
      m.isActive && 
      m.sourceDocumentId === sourceDocId && 
      m.sourceSectionId === sourceSectionId
    );

    relevantMappings.forEach(mapping => {
      const sourceDoc = getDocumentProgress(mapping.sourceDocumentId);
      const sourceSection = sourceDoc?.sections.find(s => s.id === mapping.sourceSectionId);
      
      if (sourceSection) {
        // Update target section progress based on source progress
        const targetProgress = Math.round(sourceSection.progressPercentage * 0.8); // 80% of source progress
        const targetCompletedSubsections = Math.round((targetProgress / 100) * sourceSection.totalSubsections);
        
        // Ensure target section exists
        addOrUpdateSection(
          mapping.targetDocumentId,
          mapping.targetSectionId,
          `Evidence for ${sourceSection.title}`,
          sourceSection.totalSubsections
        );
        
        // Update target section progress
        updateSectionProgress(
          mapping.targetDocumentId,
          mapping.targetSectionId,
          targetCompletedSubsections,
          `Auto-sync from ${updatedBy}`
        );
      }
    });

    // Recalculate integrated progress
    calculateIntegratedProgress();
  }, [mappings, getDocumentProgress, addOrUpdateSection, updateSectionProgress, calculateIntegratedProgress]);

  // Get mappings for a specific section
  const getMappingsForSection = useCallback((documentId: string, sectionId: string) => {
    return mappings.filter(m => 
      (m.sourceDocumentId === documentId && m.sourceSectionId === sectionId) ||
      (m.targetDocumentId === documentId && m.targetSectionId === sectionId)
    );
  }, [mappings]);

  // Get integrated progress for a specific section
  const getIntegratedProgressForSection = useCallback((sectionId: string) => {
    return integratedProgress.find(p => p.sectionId === sectionId);
  }, [integratedProgress]);

  // Initialize integrated progress calculation
  useEffect(() => {
    calculateIntegratedProgress();
  }, [calculateIntegratedProgress]);

  return {
    mappings,
    integratedProgress,
    addMapping,
    removeMapping,
    toggleMapping,
    syncProgress,
    getMappingsForSection,
    getIntegratedProgressForSection,
    calculateIntegratedProgress
  };
}

// Hook for automatic progress synchronization
export function useAutoProgressSync() {
  const { syncProgress } = useCrossDocumentIntegration();

  // Function to trigger sync when subsection action occurs
  const triggerSync = useCallback((documentId: string, sectionId: string, updatedBy: string) => {
    if (documentId === 'doc-2') {
      // Only sync from doc-2 to doc-3
      syncProgress(documentId, sectionId, updatedBy);
    }
  }, [syncProgress]);

  return { triggerSync };
}
