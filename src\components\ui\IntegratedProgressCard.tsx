'use client';

import { useState } from 'react';
import { 
  ChartBarIcon, 
  LinkIcon, 
  CheckCircleIcon, 
  ClockIcon,
  InformationCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { IntegratedProgress, CrossDocumentMapping } from '@/hooks/useCrossDocumentIntegration';

interface IntegratedProgressCardProps {
  progress: IntegratedProgress;
  showDetails?: boolean;
  className?: string;
}

export default function IntegratedProgressCard({ 
  progress, 
  showDetails = false,
  className = '' 
}: IntegratedProgressCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 60) return 'text-blue-600 bg-blue-100';
    if (percentage >= 40) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-blue-500';
    if (percentage >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-lg ${getProgressColor(progress.combinedProgress)}`}>
              <LinkIcon className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-gotham-rounded text-lg font-semibold text-gray-900">
                {progress.sectionTitle}
              </h3>
              <p className="font-gotham text-sm text-gray-600 mt-1">
                Progress Terintegrasi dari Prosedur dan Instruksi Kerja
              </p>
            </div>
          </div>
          
          {/* Combined Progress Badge */}
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getProgressColor(progress.combinedProgress)}`}>
            {progress.combinedProgress}%
          </div>
        </div>
      </div>

      {/* Progress Details */}
      <div className="p-6">
        {/* Progress Bars */}
        <div className="space-y-4">
          {/* Source Progress (Prosedur dan Instruksi Kerja) */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="font-gotham text-sm font-medium text-gray-700">
                Prosedur dan Instruksi Kerja
              </span>
              <span className="font-gotham text-sm font-semibold text-gray-900">
                {progress.sourceProgress}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(progress.sourceProgress)}`}
                style={{ width: `${progress.sourceProgress}%` }}
              />
            </div>
          </div>

          {/* Arrow Indicator */}
          <div className="flex justify-center">
            <ArrowRightIcon className="h-5 w-5 text-gray-400" />
          </div>

          {/* Target Progress (Evidence Mapping) */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="font-gotham text-sm font-medium text-gray-700">
                Evidence Mapping Klausul
              </span>
              <span className="font-gotham text-sm font-semibold text-gray-900">
                {progress.targetProgress}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(progress.targetProgress)}`}
                style={{ width: `${progress.targetProgress}%` }}
              />
            </div>
          </div>

          {/* Combined Progress */}
          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <span className="font-gotham text-sm font-semibold text-gray-900">
                Progress Gabungan
              </span>
              <span className="font-gotham text-sm font-bold text-gray-900">
                {progress.combinedProgress}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-300 ${getProgressBarColor(progress.combinedProgress)}`}
                style={{ width: `${progress.combinedProgress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Last Updated */}
        <div className="flex items-center space-x-2 mt-4 pt-4 border-t border-gray-100">
          <ClockIcon className="h-4 w-4 text-gray-400" />
          <span className="font-gotham text-xs text-gray-500">
            Terakhir diperbarui: {formatLastUpdated(progress.lastUpdated)}
          </span>
        </div>

        {/* Show Details Button */}
        {showDetails && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="w-full mt-4 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            {isExpanded ? 'Sembunyikan Detail' : 'Lihat Detail Mapping'}
          </button>
        )}

        {/* Expanded Details */}
        {isExpanded && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-gotham-rounded text-sm font-semibold text-gray-900 mb-3">
              Detail Mapping
            </h4>
            <div className="space-y-3">
              {progress.mappings.map((mapping, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="p-1 bg-blue-100 rounded">
                      <LinkIcon className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-gotham text-sm font-medium text-gray-900">
                        {mapping.mappingType === 'one_to_one' ? 'Satu ke Satu' : 
                         mapping.mappingType === 'one_to_many' ? 'Satu ke Banyak' : 'Banyak ke Satu'}
                      </p>
                      <p className="font-gotham text-xs text-gray-500">
                        {mapping.sourceDocumentId} → {mapping.targetDocumentId}
                      </p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    mapping.isActive ? 'text-green-700 bg-green-100' : 'text-gray-500 bg-gray-100'
                  }`}>
                    {mapping.isActive ? 'Aktif' : 'Nonaktif'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Status Indicator */}
      <div className="px-6 py-3 bg-gray-50 rounded-b-xl">
        <div className="flex items-center space-x-2">
          {progress.combinedProgress >= 100 ? (
            <>
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              <span className="font-gotham text-sm font-medium text-green-700">
                Selesai
              </span>
            </>
          ) : progress.combinedProgress > 0 ? (
            <>
              <ChartBarIcon className="h-4 w-4 text-blue-600" />
              <span className="font-gotham text-sm font-medium text-blue-700">
                Dalam Progress
              </span>
            </>
          ) : (
            <>
              <InformationCircleIcon className="h-4 w-4 text-gray-500" />
              <span className="font-gotham text-sm font-medium text-gray-600">
                Belum Dimulai
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Component for displaying multiple integrated progress cards
interface IntegratedProgressListProps {
  progressList: IntegratedProgress[];
  showDetails?: boolean;
  className?: string;
}

export function IntegratedProgressList({ 
  progressList, 
  showDetails = false,
  className = '' 
}: IntegratedProgressListProps) {
  if (progressList.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <InformationCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="font-gotham-rounded text-lg font-medium text-gray-900 mb-2">
          Belum Ada Progress Terintegrasi
        </h3>
        <p className="font-gotham text-gray-600">
          Progress akan muncul setelah ada aktivitas di Prosedur dan Instruksi Kerja
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {progressList.map((progress) => (
        <IntegratedProgressCard
          key={progress.sectionId}
          progress={progress}
          showDetails={showDetails}
        />
      ))}
    </div>
  );
}
