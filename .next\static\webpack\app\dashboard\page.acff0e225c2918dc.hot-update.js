"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiL1VzZXJzL2VyaW5oci9Eb3dubG9hZHMvd2VibWFnYW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction ProgramIndicator() {\n    _s();\n    const { activeProgram } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__.useProgram)();\n    if (!activeProgram) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden md:flex items-center space-x-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-4 w-4 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-blue-700\",\n                children: activeProgram.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgramIndicator, \"g/tdYF8axooZDa+8hPRSOzOtkns=\", false, function() {\n    return [\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_5__.useProgram\n    ];\n});\n_c = ProgramIndicator;\nfunction AppLayout(param) {\n    let { children } = param;\n    _s1();\n    const { user, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Function to determine if a menu item is active\n    const isActive = (path)=>{\n        if (path === '/dashboard') {\n            return pathname === '/dashboard';\n        }\n        return pathname.startsWith(path);\n    };\n    // Function to get nav link classes\n    const getNavLinkClasses = function(path) {\n        let isMobile = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const baseClasses = \"font-gotham-rounded font-medium transition-colors duration-200\";\n        const mobileClasses = isMobile ? \"block px-3 py-2 rounded-md text-base\" : \"\";\n        if (isActive(path)) {\n            // Active state classes\n            const activeClasses = isMobile ? \"text-primary bg-blue-50 border-l-4 border-primary\" : \"text-primary border-b-2 border-primary\";\n            return \"\".concat(baseClasses, \" \").concat(mobileClasses, \" \").concat(activeClasses);\n        } else {\n            // Inactive state classes\n            const inactiveClasses = isMobile ? \"text-secondary hover:text-primary hover:bg-gray-50\" : \"text-secondary hover:text-primary\";\n            return \"\".concat(baseClasses, \" \").concat(mobileClasses, \" \").concat(inactiveClasses);\n        }\n    };\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const closeMobileMenu = ()=>{\n        setIsMobileMenuOpen(false);\n    };\n    if (!user) {\n        // Redirect to login instead of showing loading\n        if (true) {\n            window.location.href = '/login';\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Mengarahkan ke halaman login...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/smap-logo.png\",\n                                                alt: \"SMAP Logo\",\n                                                width: 120,\n                                                height: 48,\n                                                className: \"h-12 w-auto transition-transform duration-200 hover:scale-105\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/config\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Config\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/manage-group\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Manage Group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/manage-program\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Manage Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/repository\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Risk Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgramIndicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-gotham text-sm text-secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs ml-1\",\n                                                            children: [\n                                                                \"(\",\n                                                                (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        logout();\n                                                        window.location.href = '/login';\n                                                    },\n                                                    className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMobileMenu,\n                                                className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary\",\n                                                \"aria-expanded\": \"false\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden \".concat(isMobileMenuOpen ? 'block' : 'hidden'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/config\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Config\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/manage-group\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Manage Group\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/manage-program\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Manage Program\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/repository\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Risk Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 pb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-base font-medium text-gray-800\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    logout();\n                                                    window.location.href = '/login';\n                                                },\n                                                className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200 w-full text-left\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s1(AppLayout, \"+P0DkPZELt5bvoQmdq8+uVdtIdQ=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c1 = AppLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProgramIndicator\");\n$RefreshReg$(_c1, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ })

});