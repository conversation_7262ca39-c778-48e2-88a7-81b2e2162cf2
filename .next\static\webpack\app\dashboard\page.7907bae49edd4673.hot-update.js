"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,CalendarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction ProgramIndicator() {\n    _s();\n    const { activeProgram } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_4__.useProgram)();\n    if (!activeProgram) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden md:flex items-center space-x-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-blue-700\",\n                children: activeProgram.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(ProgramIndicator, \"g/tdYF8axooZDa+8hPRSOzOtkns=\", false, function() {\n    return [\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_4__.useProgram\n    ];\n});\n_c = ProgramIndicator;\nfunction AppLayout(param) {\n    let { children } = param;\n    _s1();\n    const { user, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const closeMobileMenu = ()=>{\n        setIsMobileMenuOpen(false);\n    };\n    if (!user) {\n        // Redirect to login instead of showing loading\n        if (true) {\n            window.location.href = '/login';\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Mengarahkan ke halaman login...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: \"/smap-logo.png\",\n                                                alt: \"SMAP Logo\",\n                                                width: 120,\n                                                height: 48,\n                                                className: \"h-12 w-auto transition-transform duration-200 hover:scale-105\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/config\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Config\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/manage-group\",\n                                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                            children: \"Manage Group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/manage-program\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Manage Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/repository\",\n                                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary transition-colors duration-200\",\n                                                    children: \"Risk Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgramIndicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-gotham text-sm text-secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs ml-1\",\n                                                            children: [\n                                                                \"(\",\n                                                                (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        logout();\n                                                        window.location.href = '/login';\n                                                    },\n                                                    className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMobileMenu,\n                                                className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary\",\n                                                \"aria-expanded\": \"false\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_CalendarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden \".concat(isMobileMenuOpen ? 'block' : 'hidden'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/config\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Config\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/manage-group\",\n                                            onClick: closeMobileMenu,\n                                            className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                            children: \"Manage Group\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/manage-program\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Manage Program\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/repository\",\n                                    onClick: closeMobileMenu,\n                                    className: \"font-gotham-rounded font-medium text-secondary hover:text-primary hover:bg-gray-50 block px-3 py-2 rounded-md text-base transition-colors duration-200\",\n                                    children: \"Risk Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 pb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-base font-medium text-gray-800\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    logout();\n                                                    window.location.href = '/login';\n                                                },\n                                                className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200 w-full text-left\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s1(AppLayout, \"FvPo28FA3zg9qAJCJmritvSMxGg=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c1 = AppLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProgramIndicator\");\n$RefreshReg$(_c1, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ })

});