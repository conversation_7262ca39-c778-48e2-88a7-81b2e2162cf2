"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dokumen/doc-1/page",{

/***/ "(app-pages-browser)/./src/app/dokumen/doc-1/page.tsx":
/*!****************************************!*\
  !*** ./src/app/dokumen/doc-1/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PedomanPeraturanPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* harmony import */ var _hooks_useSubsectionActions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubsectionActions */ \"(app-pages-browser)/./src/hooks/useSubsectionActions.ts\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(app-pages-browser)/./src/components/layout/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ArrowRightIcon,BookOpenIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,PlusIcon,ShieldCheckIcon,SpeakerWaveIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n// Sub-documents for Pedoman Peraturan Perusahaan\nconst SUB_DOCUMENTS = [];\n// Mock files data\nconst MOCK_FILES = [\n    {\n        id: '1',\n        name: 'Manual_SMAP_v2.1.pdf',\n        type: 'pdf',\n        size: '2.4 MB',\n        uploadDate: '2024-01-15',\n        uploadedBy: 'Admin Super'\n    },\n    {\n        id: '2',\n        name: 'Struktur_Organisasi_SMAP.docx',\n        type: 'docx',\n        size: '1.8 MB',\n        uploadDate: '2024-01-14',\n        uploadedBy: 'Admin Biasa'\n    }\n];\nfunction FilePreviewModal(param) {\n    let { isOpen, onClose, fileName, fileType, fileContent, mimeType } = param;\n    if (!isOpen) return null;\n    const renderPreview = ()=>{\n        const extension = fileType.toLowerCase();\n        // If no content available, show placeholder\n        if (!fileContent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"No Preview Available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"File content not available for preview.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this);\n        }\n        if (extension === 'pdf') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                    src: fileContent,\n                    className: \"w-full h-full border-0\",\n                    title: \"PDF Preview: \".concat(fileName)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'jpg',\n            'jpeg',\n            'png',\n            'gif',\n            'webp'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: fileContent,\n                    alt: fileName,\n                    className: \"max-w-full max-h-full object-contain\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'doc',\n            'docx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Word Document\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview dokumen Word tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'xls',\n            'xlsx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-green-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"Excel Spreadsheet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview spreadsheet tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this);\n        }\n        if ([\n            'ppt',\n            'pptx'\n        ].includes(extension)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-16 w-16 text-orange-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-700 mb-2\",\n                            children: \"PowerPoint Presentation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-sm text-gray-500\",\n                            children: fileName\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-xs text-gray-400 mt-2\",\n                            children: \"Preview presentasi tidak tersedia. Silakan download untuk melihat file.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this);\n        }\n        // Default preview for unknown file types\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[600px] bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-gray-700 mb-2\",\n                        children: \"File Preview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-sm text-gray-500\",\n                        children: fileName\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-gotham text-xs text-gray-400 mt-2\",\n                        children: \"Preview tidak tersedia untuk tipe file ini. Silakan download untuk melihat file.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-6xl w-full mx-4 max-h-[95vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                children: \"File Preview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        renderPreview(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (fileContent) {\n                                            const link = document.createElement('a');\n                                            link.href = fileContent;\n                                            link.download = fileName;\n                                            document.body.appendChild(link);\n                                            link.click();\n                                            document.body.removeChild(link);\n                                        } else {\n                                            alert('File content not available for download');\n                                        }\n                                    },\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_c = FilePreviewModal;\nfunction FileManagerModal(param) {\n    let { isOpen, onClose, sectionId, sectionTitle, userRole, onFileUploaded, onFileDeleted } = param;\n    _s();\n    // Store files per section in localStorage\n    const [sectionFiles, setSectionFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load files from localStorage after component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FileManagerModal.useEffect\": ()=>{\n            const loadSectionFiles = {\n                \"FileManagerModal.useEffect.loadSectionFiles\": ()=>{\n                    try {\n                        const saved = localStorage.getItem('doc-1-section-files');\n                        if (saved) {\n                            setSectionFiles(JSON.parse(saved));\n                        }\n                    } catch (error) {\n                        console.error('Error loading section files from localStorage:', error);\n                    }\n                }\n            }[\"FileManagerModal.useEffect.loadSectionFiles\"];\n            loadSectionFiles();\n        }\n    }[\"FileManagerModal.useEffect\"], []);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewFile, setPreviewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        content: '',\n        mimeType: ''\n    });\n    // Close preview when file manager is closed\n    const handleClose = ()=>{\n        setShowPreview(false);\n        onClose();\n    };\n    // Get files for current section\n    const currentSectionFiles = sectionFiles[sectionId] || [];\n    const hasFile = currentSectionFiles.length > 0;\n    // Check if user can upload (not karyawan or reviewer)\n    const canUpload = userRole !== 'karyawan' && userRole !== 'reviewer';\n    // Save to localStorage whenever sectionFiles changes\n    const updateSectionFiles = (newSectionFiles)=>{\n        setSectionFiles(newSectionFiles);\n        try {\n            localStorage.setItem('doc-1-section-files', JSON.stringify(newSectionFiles));\n        } catch (error) {\n            console.error('Error saving section files to localStorage:', error);\n        }\n    };\n    if (!isOpen) return null;\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            setSelectedFile(file);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!selectedFile || hasFile) return; // Prevent upload if already has file\n        setIsUploading(true);\n        // Convert file to base64 for storage\n        const fileReader = new FileReader();\n        fileReader.onload = ()=>{\n            const fileContent = fileReader.result;\n            const newFile = {\n                id: Date.now().toString(),\n                name: selectedFile.name,\n                type: selectedFile.name.split('.').pop() || 'unknown',\n                size: \"\".concat((selectedFile.size / 1024 / 1024).toFixed(1), \" MB\"),\n                uploadDate: new Date().toISOString().split('T')[0],\n                uploadedBy: 'Current User',\n                content: fileContent,\n                mimeType: selectedFile.type\n            };\n            // Update files for this section only (max 1 file)\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: [\n                    newFile\n                ] // Only one file per section\n            };\n            updateSectionFiles(newSectionFiles);\n            setSelectedFile(null);\n            setIsUploading(false);\n            // Trigger progress tracking\n            if (onFileUploaded) {\n                onFileUploaded(sectionId);\n            }\n            alert('File berhasil diupload!');\n        };\n        fileReader.onerror = ()=>{\n            setIsUploading(false);\n            alert('Error reading file!');\n        };\n        // Read file as data URL (base64)\n        fileReader.readAsDataURL(selectedFile);\n    };\n    const handleDelete = (fileId)=>{\n        if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {\n            const newSectionFiles = {\n                ...sectionFiles,\n                [sectionId]: currentSectionFiles.filter((file)=>file.id !== fileId)\n            };\n            updateSectionFiles(newSectionFiles);\n            // Trigger progress tracking for file deletion\n            if (onFileDeleted) {\n                onFileDeleted(sectionId);\n            }\n            alert('File berhasil dihapus!');\n        }\n    };\n    const handleView = (file)=>{\n        setPreviewFile({\n            name: file.name,\n            type: file.type,\n            content: file.content,\n            mimeType: file.mimeType\n        });\n        setShowPreview(true);\n    };\n    const handleDownload = (fileName)=>{\n        alert(\"Mengunduh file: \".concat(fileName));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                        children: \"File Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-gray-600 text-sm mt-1\",\n                                        children: sectionTitle\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                        children: [\n                            canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"Upload File Baru\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    hasFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-yellow-800 text-sm\",\n                                            children: \"⚠️ Sub-dokumen ini sudah memiliki file. Hapus file yang ada terlebih dahulu untuk upload file baru.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                onChange: handleFileSelect,\n                                                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\",\n                                                accept: \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg p-3 border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                            children: selectedFile.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-xs text-gray-500\",\n                                                                            children: [\n                                                                                (selectedFile.size / 1024 / 1024).toFixed(1),\n                                                                                \" MB\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpload,\n                                                            disabled: isUploading,\n                                                            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-gotham font-medium text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: isUploading ? 'Uploading...' : 'Upload'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            !canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-gotham-rounded font-semibold text-blue-900 text-sm\",\n                                                    children: \"Mode View Only\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-blue-800 text-xs\",\n                                                    children: \"Anda dapat melihat dan mendownload file, namun tidak dapat mengupload file baru.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-gotham-rounded font-semibold text-gray-900 mb-3\",\n                                        children: \"File yang Tersedia\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            currentSectionFiles.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                                children: file.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 473,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3 text-xs text-gray-500 font-gotham\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.size\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 475,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 476,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadDate\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 477,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 478,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: file.uploadedBy\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 479,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleView(file),\n                                                                        className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\",\n                                                                        title: \"View File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDownload(file.name),\n                                                                        className: \"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors\",\n                                                                        title: \"Download File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    canUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDelete(file.id),\n                                                                        className: \"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                                                        title: \"Delete File\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, file.id, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            currentSectionFiles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-500 text-sm\",\n                                                        children: \"Belum ada file yang diupload untuk sub-dokumen ini\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-gotham text-gray-400 text-xs mt-1\",\n                                                        children: \"Maksimal 1 file per sub-dokumen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FilePreviewModal, {\n                isOpen: showPreview,\n                onClose: ()=>setShowPreview(false),\n                fileName: previewFile.name,\n                fileType: previewFile.type,\n                fileContent: previewFile.content,\n                mimeType: previewFile.mimeType\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 372,\n        columnNumber: 5\n    }, this);\n}\n_s(FileManagerModal, \"WwKpo+R8DCEmGqCXleOmG/2pUVs=\");\n_c1 = FileManagerModal;\nfunction AddSectionModal(param) {\n    let { isOpen, onClose, onSave, subDocTitle } = param;\n    _s1();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim()) {\n            onSave(title.trim(), description.trim());\n            setTitle('');\n            setDescription('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: subDocTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 40\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n            lineNumber: 575,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 574,\n        columnNumber: 5\n    }, this);\n}\n_s1(AddSectionModal, \"1UKQWTfo2RWmkwPNsekAdQbvqFk=\");\n_c2 = AddSectionModal;\nfunction AddSubSectionModal(param) {\n    let { isOpen, onClose, onSave, sectionTitle } = param;\n    _s2();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('upload');\n    const [formUrl, setFormUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim() && (type === 'upload' || type === 'form' && formUrl.trim())) {\n            onSave(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);\n            setTitle('');\n            setDescription('');\n            setType('upload');\n            setFormUrl('');\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        setType('upload');\n        setFormUrl('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900\",\n                            children: \"Tambah Sub-Section Baru\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-gotham text-sm text-gray-600 mb-4\",\n                    children: [\n                        \"Menambah sub-section baru untuk: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: sectionTitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 44\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Judul Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: title,\n                                    onChange: (e)=>setTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan judul sub-section...\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"Deskripsi \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 font-normal\",\n                                            children: \"(opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: description,\n                                    onChange: (e)=>setDescription(e.target.value),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Masukkan deskripsi sub-section (opsional)...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tipe Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"upload\",\n                                                    checked: type === 'upload',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 text-blue-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Upload Dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk upload file dokumen\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"subsectionType\",\n                                                    value: \"form\",\n                                                    checked: type === 'form',\n                                                    onChange: (e)=>setType(e.target.value),\n                                                    className: \"mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-green-100 text-green-600 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham font-medium text-gray-900\",\n                                                                    children: \"Isi Formulir\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-gotham text-sm text-gray-600\",\n                                                                    children: \"Sub-section untuk mengisi formulir online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, this),\n                        type === 'form' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block font-gotham text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"URL Formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formUrl,\n                                    onChange: (e)=>setFormUrl(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-gotham\",\n                                    placeholder: \"Contoh: /forms/risk-assessment\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                    children: \"Masukkan URL relatif atau absolut untuk formulir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim() || type === 'form' && !formUrl.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n            lineNumber: 679,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 678,\n        columnNumber: 5\n    }, this);\n}\n_s2(AddSubSectionModal, \"j4muNlo4jrHMXOkSO9GEWhR03Ec=\");\n_c3 = AddSubSectionModal;\nfunction DeleteConfirmModal(param) {\n    let { isOpen, onClose, onConfirm, title, type } = param;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-lg font-semibold text-red-900\",\n                            children: [\n                                \"Hapus \",\n                                type === 'section' ? 'Section' : 'Sub-Section'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 828,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-red-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham font-medium text-gray-900\",\n                                            children: [\n                                                \"Apakah Anda yakin ingin menghapus \",\n                                                type === 'section' ? 'section' : 'sub-section',\n                                                \" ini?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-600 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    '\"',\n                                                    title,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-gotham text-sm text-red-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Peringatan:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Tindakan ini tidak dapat dibatalkan.\",\n                                    type === 'section' ? ' Semua sub-section dan file yang terkait akan ikut terhapus.' : ' File yang terkait dengan sub-section ini akan ikut terhapus.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-gotham font-medium\",\n                            children: \"Batal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onConfirm,\n                            className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-gotham font-medium\",\n                            children: \"Hapus\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n            lineNumber: 826,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 825,\n        columnNumber: 5\n    }, this);\n}\n_c4 = DeleteConfirmModal;\nfunction PedomanPeraturanPage() {\n    _s3();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { dynamicSections: dynamicMainSections, dynamicSubSections, updateDynamicSections: updateDynamicMainSections, updateDynamicSubSections } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument)('doc1');\n    // Progress tracking with subsection actions\n    const { addSubsectionAction, removeSubsectionAction } = (0,_hooks_useSubsectionActions__WEBPACK_IMPORTED_MODULE_4__.useSubsectionActions)('doc-1');\n    const [expandedSection, setExpandedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFileManager, setShowFileManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showAddSection, setShowAddSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddSubSection, setShowAddSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSubDocId, setCurrentSubDocId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [deleteTarget, setDeleteTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: 'section',\n        id: '',\n        title: ''\n    });\n    if (!user) return null;\n    const handleSectionToggle = (sectionId)=>{\n        setExpandedSection(expandedSection === sectionId ? null : sectionId);\n    };\n    const handleSubSectionToggle = (subSectionId)=>{\n        setExpandedSection(expandedSection === subSectionId ? null : subSectionId);\n    };\n    // Calculate section progress based on subsections\n    const calculateSectionProgress = (sectionId)=>{\n        const subSections = dynamicSubSections[sectionId] || [];\n        const totalSubsections = subSections.length;\n        if (totalSubsections === 0) return {\n            progress: 0,\n            completed: 0,\n            total: 0\n        };\n        // Check how many subsections have files uploaded\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        let completedSubsections = 0;\n        subSections.forEach((subSection)=>{\n            const files = sectionFiles[subSection.id] || [];\n            if (files.length > 0) {\n                completedSubsections++;\n            }\n        });\n        const progress = Math.round(completedSubsections / totalSubsections * 100);\n        return {\n            progress,\n            completed: completedSubsections,\n            total: totalSubsections\n        };\n    };\n    // Track subsection action when file is uploaded\n    const trackSubsectionAction = (subsectionId, actionType)=>{\n        if (!user) return;\n        addSubsectionAction(subsectionId, actionType, user.id, user.name, \"Action performed on \".concat(new Date().toLocaleString()));\n    };\n    // Remove subsection action when file is deleted\n    const removeSubsectionActionTracking = (subsectionId)=>{\n        removeSubsectionAction(subsectionId);\n    };\n    // Save sections and subsections data to localStorage for progress calculation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PedomanPeraturanPage.useEffect\": ()=>{\n            const allSections = [\n                ...SUB_DOCUMENTS,\n                ...dynamicMainSections\n            ];\n            // Save sections data\n            localStorage.setItem('doc-1-dynamic-sections', JSON.stringify(allSections));\n            // Save subsections data\n            localStorage.setItem('doc-1-dynamic-subsections', JSON.stringify(dynamicSubSections));\n            console.log('Doc-1 sections saved to localStorage:', allSections);\n            console.log('Doc-1 subsections saved to localStorage:', dynamicSubSections);\n        }\n    }[\"PedomanPeraturanPage.useEffect\"], [\n        dynamicMainSections,\n        dynamicSubSections\n    ]);\n    const handleUpload = (subSectionId)=>{\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    const handleViewFile = (subSectionId)=>{\n        // Get files from localStorage for this section\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[subSectionId] || [];\n        if (filesForSection.length === 0) {\n            alert('Belum ada file yang diupload untuk sub-dokumen ini.');\n            return;\n        }\n        // If there's a file, show it in the file manager\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    // Handle adding new main section (E, F, G, etc.)\n    const handleAddMainSection = ()=>{\n        setShowAddSection(true);\n    };\n    // Handle adding new sub-section (A.4, B.1, etc.)\n    const handleAddSubSection = (subDocId)=>{\n        setCurrentSubDocId(subDocId);\n        setShowAddSubSection(true);\n    };\n    // Save new main section\n    const handleSaveNewMainSection = (title, description)=>{\n        const newSectionId = \"dynamic-section-\".concat(Date.now());\n        const newSection = {\n            id: newSectionId,\n            title: title,\n            description: description,\n            iconType: 'DocumentTextIcon',\n            color: 'bg-purple-100 text-purple-600',\n            hoverColor: 'hover:border-purple-500 hover:bg-purple-50',\n            iconHoverColor: 'hover:text-purple-600',\n            subSections: [],\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSections = [\n            ...dynamicMainSections,\n            newSection\n        ];\n        updateDynamicMainSections(updatedSections);\n        setShowAddSection(false);\n    };\n    // Save new sub-section\n    const handleSaveNewSubSection = (title, description, type, formUrl)=>{\n        const subDocSubSections = dynamicSubSections[currentSubDocId] || [];\n        const newSubSectionId = \"\".concat(currentSubDocId, \"-subsection-\").concat(Date.now());\n        const newSubSection = {\n            id: newSubSectionId,\n            title: title,\n            description: description,\n            type: type,\n            formUrl: type === 'form' ? formUrl : undefined,\n            createdAt: new Date().toISOString(),\n            createdBy: user.name\n        };\n        const updatedSubSections = {\n            ...dynamicSubSections,\n            [currentSubDocId]: [\n                ...subDocSubSections,\n                newSubSection\n            ]\n        };\n        updateDynamicSubSections(updatedSubSections);\n        setShowAddSubSection(false);\n        setCurrentSubDocId('');\n    };\n    // Handle delete section/sub-section\n    const handleDeleteRequest = (type, id, title)=>{\n        setDeleteTarget({\n            type,\n            id,\n            title\n        });\n        setShowDeleteConfirm(true);\n    };\n    const handleConfirmDelete = ()=>{\n        if (deleteTarget.type === 'section') {\n            // Delete main section\n            const updatedSections = dynamicMainSections.filter((section)=>section.id !== deleteTarget.id);\n            updateDynamicMainSections(updatedSections);\n            // Also delete all sub-sections for this section\n            const updatedSubSections = {\n                ...dynamicSubSections\n            };\n            delete updatedSubSections[deleteTarget.id];\n            updateDynamicSubSections(updatedSubSections);\n            // Delete all files for this section and its sub-sections\n            cleanupSectionFiles(deleteTarget.id);\n        } else if (deleteTarget.type === 'subsection') {\n            // Delete sub-section\n            const sectionId = findSectionIdForSubSection(deleteTarget.id);\n            if (sectionId) {\n                const updatedSubSections = {\n                    ...dynamicSubSections,\n                    [sectionId]: dynamicSubSections[sectionId].filter((subSection)=>subSection.id !== deleteTarget.id)\n                };\n                updateDynamicSubSections(updatedSubSections);\n                // Delete files for this sub-section\n                cleanupSubSectionFiles(deleteTarget.id);\n            }\n        }\n        setShowDeleteConfirm(false);\n        setDeleteTarget({\n            type: 'section',\n            id: '',\n            title: ''\n        });\n    };\n    // Helper function to find section ID for a sub-section\n    const findSectionIdForSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return sectionId;\n            }\n        }\n        return null;\n    };\n    // Cleanup files and actions when deleting section/sub-section\n    const cleanupSectionFiles = (sectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                // Remove files for the section itself\n                delete sectionFiles[sectionId];\n                // Remove action tracking for the section\n                removeSubsectionAction(sectionId);\n                // Remove files for all sub-sections of this section\n                const subSections = dynamicSubSections[sectionId] || [];\n                subSections.forEach((subSection)=>{\n                    delete sectionFiles[subSection.id];\n                    // Remove action tracking for each subsection\n                    removeSubsectionAction(subSection.id);\n                });\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n            }\n        } catch (error) {\n            console.error('Error cleaning up section files:', error);\n        }\n    };\n    const cleanupSubSectionFiles = (subSectionId)=>{\n        try {\n            const savedFiles = localStorage.getItem('doc-1-section-files');\n            if (savedFiles) {\n                const sectionFiles = JSON.parse(savedFiles);\n                delete sectionFiles[subSectionId];\n                localStorage.setItem('doc-1-section-files', JSON.stringify(sectionFiles));\n                // Remove action tracking for the subsection\n                removeSubsectionAction(subSectionId);\n            }\n        } catch (error) {\n            console.error('Error cleaning up sub-section files:', error);\n        }\n    };\n    // Get icon component from string identifier\n    const getIconComponent = (iconType)=>{\n        const iconMap = {\n            'DocumentTextIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            'ShieldCheckIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            'BookOpenIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            'ExclamationTriangleIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            'SpeakerWaveIcon': _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        };\n        return iconMap[iconType] || _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n    };\n    // Get all sections (static + dynamic)\n    const getAllSections = ()=>{\n        // Add icon component and ensure all required properties for dynamic sections\n        const dynamicSectionsWithIcons = dynamicMainSections.map((section)=>({\n                ...section,\n                icon: getIconComponent(section.iconType || 'DocumentTextIcon'),\n                subSections: section.subSections || [],\n                color: section.color || 'bg-purple-100 text-purple-600',\n                hoverColor: section.hoverColor || 'hover:border-purple-500 hover:bg-purple-50',\n                iconHoverColor: section.iconHoverColor || 'hover:text-purple-600'\n            }));\n        return [\n            ...SUB_DOCUMENTS,\n            ...dynamicSectionsWithIcons\n        ];\n    };\n    // Get all sub-sections for a section (static + dynamic)\n    const getAllSubSections = (sectionId)=>{\n        const staticSection = SUB_DOCUMENTS.find((doc)=>doc.id === sectionId);\n        const staticSubSections = staticSection ? staticSection.subSections : [];\n        const dynamicSubSectionsForSection = dynamicSubSections[sectionId] || [];\n        return [\n            ...staticSubSections,\n            ...dynamicSubSectionsForSection\n        ];\n    };\n    // Check if section can be deleted (only dynamic sections)\n    const canDeleteSection = (sectionId)=>{\n        return dynamicMainSections.some((section)=>section.id === sectionId);\n    };\n    // Check if sub-section can be deleted (only dynamic sub-sections)\n    const canDeleteSubSection = (subSectionId)=>{\n        for(const sectionId in dynamicSubSections){\n            if (dynamicSubSections[sectionId].some((subSection)=>subSection.id === subSectionId)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    const handleBack = ()=>{\n        window.history.back();\n    };\n    const getSectionTitle = (sectionId)=>{\n        const sectionTitles = {\n            'manual-smap': 'Manual SMAP',\n            'struktur-organisasi': 'Struktur Organisasi SMAP Telkom',\n            'kebijakan-anti-suap': 'Kebijakan Anti Suap Telkom',\n            'formulir-risk-assessment': 'Formulir Penilaian Risiko Penyuapan (Risk Assessment)',\n            'sub-doc-2': 'Code of Conduct',\n            'sub-doc-3': 'Conflict of Interest',\n            'sub-doc-4': 'Whistle Blower System'\n        };\n        // Check if it's a dynamic main section\n        const dynamicMainSection = dynamicMainSections.find((section)=>section.id === sectionId);\n        if (dynamicMainSection) {\n            return dynamicMainSection.title;\n        }\n        // Check if it's a dynamic sub-section\n        for(const subDocId in dynamicSubSections){\n            const subSections = dynamicSubSections[subDocId];\n            const dynamicSubSection = subSections.find((subSection)=>subSection.id === sectionId);\n            if (dynamicSubSection) {\n                return dynamicSubSection.title;\n            }\n        }\n        return sectionTitles[sectionId] || sectionId;\n    };\n    const hasFileInSection = (sectionId)=>{\n        const savedFiles = localStorage.getItem('doc-1-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[sectionId] || [];\n        return filesForSection.length > 0;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-100 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"font-gotham-rounded text-3xl font-bold text-gray-900\",\n                                                    children: \"Pedoman Peraturan Perusahaan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-gray-600 mt-1\",\n                                                    children: \"Dokumen pedoman dan peraturan perusahaan terkait anti penyuapan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1250,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard\",\n                                            className: \"hover:text-red-600 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"›\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900\",\n                                            children: \"Pedoman Peraturan Perusahaan\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 1257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Kembali\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 1241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                getAllSections().map((subDoc, index)=>{\n                                    const IconComponent = subDoc.icon;\n                                    const isExpanded = expandedSection === subDoc.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 rounded-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                onClick: ()=>handleSectionToggle(subDoc.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-xl \".concat(subDoc.color),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                        className: \"h-6 w-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1294,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-gotham-rounded text-lg font-semibold text-gray-900 mb-2\",\n                                                                            children: [\n                                                                                String.fromCharCode(65 + index),\n                                                                                \". \",\n                                                                                subDoc.title\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 1297,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-gotham text-gray-600 text-sm leading-relaxed\",\n                                                                            children: subDoc.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                            lineNumber: 1300,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 1296,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                canDeleteSection(subDoc.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation(); // Prevent section toggle\n                                                                        handleDeleteRequest('section', subDoc.id, subDoc.title);\n                                                                    },\n                                                                    className: \"p-2 border border-red-300 rounded-lg hover:bg-red-50 hover:border-red-400 transition-all duration-200 group\",\n                                                                    title: \"Hapus Section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-red-500 group-hover:text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1316,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 1308,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 border border-gray-300 rounded-lg transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 1321,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                            lineNumber: 1305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 19\n                                            }, this),\n                                            isExpanded && (getAllSubSections(subDoc.id).length > 0 || user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            getAllSubSections(subDoc.id).map((subSection, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2 mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"p-1 rounded \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'),\n                                                                                                children: subSection.type === 'form' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                    lineNumber: 1351,\n                                                                                                    columnNumber: 41\n                                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                    lineNumber: 1353,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1345,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"font-gotham-rounded text-base font-semibold text-gray-900\",\n                                                                                                children: [\n                                                                                                    String.fromCharCode(65 + index),\n                                                                                                    \".\",\n                                                                                                    subIndex + 1,\n                                                                                                    \" \",\n                                                                                                    subSection.title\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1356,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"px-2 py-0.5 text-xs font-medium rounded-full \".concat(subSection.type === 'form' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'),\n                                                                                                children: subSection.type === 'form' ? 'Form' : 'Upload'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1359,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 1344,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                                        children: subSection.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 1367,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    subSection.type === 'form' && subSection.formUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-gotham text-xs text-blue-600 mt-1\",\n                                                                                        children: [\n                                                                                            \"URL: \",\n                                                                                            subSection.formUrl\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 1371,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1343,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                                children: [\n                                                                                    subSection.type === 'form' ? /* Form button - for form type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>window.open(subSection.formUrl, '_blank'),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1383,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Isi Form\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1384,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 1379,\n                                                                                        columnNumber: 37\n                                                                                    }, this) : /* Upload and View buttons - for upload type subsections */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleUpload(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1395,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Upload\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1396,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1391,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>handleViewFile(subSection.id),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subSection.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1409,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: hasFileInSection(subSection.id) ? 'View File' : 'No File'\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1410,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1401,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true),\n                                                                                    canDeleteSubSection(subSection.id) && (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteRequest('subsection', subSection.id, subSection.title),\n                                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                        title: \"Hapus Sub-Section\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1422,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"Delete\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1423,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                        lineNumber: 1417,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    subSection.id === 'formulir-risk-assessment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Buka Formulir Risk Assessment\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1435,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Isi Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1436,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1430,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>window.open('/forms/risk-assessment/view', '_blank'),\n                                                                                                className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                                                title: \"Lihat Formulir yang Telah Diisi\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                                        className: \"h-4 w-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1446,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Lihat Formulir\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                        lineNumber: 1447,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                                lineNumber: 1441,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1376,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1342,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, subSection.id, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                    lineNumber: 1338,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleAddSubSection(subDoc.id),\n                                                                        className: \"flex items-center justify-center space-x-2 mx-auto px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-gotham font-medium text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1464,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Tambah Sub-Section\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1460,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Tambahkan sub-section baru untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1467,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 1336,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1335,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 21\n                                            }, this),\n                                            isExpanded && getAllSubSections(subDoc.id).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 bg-gray-50 p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-gotham-rounded text-base font-semibold text-gray-900 mb-1\",\n                                                                        children: [\n                                                                            String.fromCharCode(65 + index),\n                                                                            \".1 Upload Dokumen \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1483,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-gray-600 text-sm\",\n                                                                        children: [\n                                                                            \"Upload file dokumen untuk \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 1482,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleUpload(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1497,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Upload\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1498,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1493,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleViewFile(subDoc.id),\n                                                                        className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subDoc.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-400 text-white hover:bg-gray-500'),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1511,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: hasFileInSection(subDoc.id) ? 'View File' : 'No File'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                                lineNumber: 1512,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                        lineNumber: 1503,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                                lineNumber: 1490,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                        lineNumber: 1481,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1480,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, subDoc.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 1282,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddMainSection,\n                                            className: \"flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tambah Section Baru\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                    lineNumber: 1531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1526,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-sm text-gray-500 mt-2\",\n                                            children: \"Tambahkan section baru\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                    lineNumber: 1525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 1276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 bg-blue-50 rounded-xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ArrowRightIcon_BookOpenIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_PlusIcon_ShieldCheckIcon_SpeakerWaveIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                            lineNumber: 1544,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 1543,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-lg font-semibold text-blue-900 mb-2\",\n                                                children: \"Informasi Dokumen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-blue-800 text-sm leading-relaxed\",\n                                                children: \"Dokumen Pedoman Peraturan Perusahaan terdiri dari 4 sub-dokumen utama yang mengatur sistem manajemen anti penyuapan. Setiap sub-dokumen memiliki fokus khusus dalam mendukung implementasi program anti penyuapan yang efektif.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                            lineNumber: 1541,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                    lineNumber: 1239,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 1238,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileManagerModal, {\n                isOpen: showFileManager,\n                onClose: ()=>setShowFileManager(false),\n                sectionId: currentSection,\n                sectionTitle: getSectionTitle(currentSection),\n                userRole: user.role,\n                onFileUploaded: (subsectionId)=>trackSubsectionAction(subsectionId, 'file_upload'),\n                onFileDeleted: (subsectionId)=>removeSubsectionActionTracking(subsectionId)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 1562,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSectionModal, {\n                isOpen: showAddSection,\n                onClose: ()=>setShowAddSection(false),\n                onSave: handleSaveNewMainSection,\n                subDocTitle: \"Pedoman Peraturan Perusahaan\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 1573,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSubSectionModal, {\n                isOpen: showAddSubSection,\n                onClose: ()=>setShowAddSubSection(false),\n                onSave: handleSaveNewSubSection,\n                sectionTitle: getSectionTitle(currentSubDocId)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 1581,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeleteConfirmModal, {\n                isOpen: showDeleteConfirm,\n                onClose: ()=>setShowDeleteConfirm(false),\n                onConfirm: handleConfirmDelete,\n                title: deleteTarget.title,\n                type: deleteTarget.type\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n                lineNumber: 1589,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-1/page.tsx\",\n        lineNumber: 1237,\n        columnNumber: 5\n    }, this);\n}\n_s3(PedomanPeraturanPage, \"sU9W6SJwqGeHgEcN614puubvqHs=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument,\n        _hooks_useSubsectionActions__WEBPACK_IMPORTED_MODULE_4__.useSubsectionActions\n    ];\n});\n_c5 = PedomanPeraturanPage;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"FilePreviewModal\");\n$RefreshReg$(_c1, \"FileManagerModal\");\n$RefreshReg$(_c2, \"AddSectionModal\");\n$RefreshReg$(_c3, \"AddSubSectionModal\");\n$RefreshReg$(_c4, \"DeleteConfirmModal\");\n$RefreshReg$(_c5, \"PedomanPeraturanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dokumen/doc-1/page.tsx\n"));

/***/ })

});