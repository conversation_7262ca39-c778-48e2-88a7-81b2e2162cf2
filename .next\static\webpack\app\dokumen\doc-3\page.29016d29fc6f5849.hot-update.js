"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dokumen/doc-3/page",{

/***/ "(app-pages-browser)/./src/app/dokumen/doc-3/page.tsx":
/*!****************************************!*\
  !*** ./src/app/dokumen/doc-3/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EvidenceMappingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ProgramContext */ \"(app-pages-browser)/./src/contexts/ProgramContext.tsx\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(app-pages-browser)/./src/components/layout/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowLeftIcon,ChevronDownIcon,ChevronUpIcon,CloudArrowUpIcon,DocumentIcon,DocumentTextIcon,EyeIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n// Sub-documents for Evidence Mapping Klausul\nconst SUB_DOCUMENTS = [];\n// Mock files data\nconst MOCK_FILES = [\n    {\n        id: '1',\n        name: 'Evidence_Mapping_Klausul_4.pdf',\n        type: 'pdf',\n        size: '2.8 MB',\n        uploadDate: '2024-01-22',\n        uploadedBy: 'Admin Super'\n    },\n    {\n        id: '2',\n        name: 'Recruitment_Evidence_Matrix.xlsx',\n        type: 'xlsx',\n        size: '1.2 MB',\n        uploadDate: '2024-01-21',\n        uploadedBy: 'Scoopers'\n    }\n];\nfunction FileManagerModal(param) {\n    let { isOpen, onClose, sectionId, sectionTitle } = param;\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(MOCK_FILES);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isOpen) return null;\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            setSelectedFile(file);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!selectedFile) return;\n        setIsUploading(true);\n        // Simulate upload delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // Add new file to the list\n        const newFile = {\n            id: (files.length + 1).toString(),\n            name: selectedFile.name,\n            type: selectedFile.name.split('.').pop() || 'unknown',\n            size: \"\".concat((selectedFile.size / (1024 * 1024)).toFixed(1), \" MB\"),\n            uploadDate: new Date().toISOString().split('T')[0],\n            uploadedBy: 'Current User'\n        };\n        setFiles([\n            ...files,\n            newFile\n        ]);\n        setSelectedFile(null);\n        setIsUploading(false);\n    };\n    const handleDeleteFile = (fileId)=>{\n        setFiles(files.filter((file)=>file.id !== fileId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-gotham-rounded text-xl font-bold text-gray-900\",\n                                children: [\n                                    \"File Manager - \",\n                                    sectionTitle\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 border-2 border-dashed border-gray-300 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                onChange: handleFileSelect,\n                                                className: \"hidden\",\n                                                id: \"file-upload\",\n                                                accept: \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                                children: \"Pilih File\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-gotham text-sm text-gray-600\",\n                                                        children: [\n                                                            selectedFile.name,\n                                                            \" (\",\n                                                            (selectedFile.size / (1024 * 1024)).toFixed(1),\n                                                            \" MB)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUpload,\n                                                        disabled: isUploading,\n                                                        className: \"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors disabled:opacity-50 font-gotham\",\n                                                        children: isUploading ? 'Uploading...' : 'Upload'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-xs text-gray-500 mt-2\",\n                                        children: \"Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX (Max 10MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-gotham-rounded text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"File yang Tersedia\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: files.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-6 w-6 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-gotham font-medium text-gray-900 text-sm\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-xs text-gray-500\",\n                                                                        children: [\n                                                                            file.size,\n                                                                            \" • Uploaded \",\n                                                                            file.uploadDate,\n                                                                            \" by \",\n                                                                            file.uploadedBy\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteFile(file.id),\n                                                                className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, file.id, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                files.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-gotham text-gray-500\",\n                                            children: \"Belum ada file yang diupload\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(FileManagerModal, \"xd9XEyN1KRMs2q1K7wvCjZpgPAc=\");\n_c = FileManagerModal;\nfunction AddSectionModal(param) {\n    let { isOpen, onClose, onAdd } = param;\n    _s1();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim()) {\n            onAdd(title.trim(), description.trim());\n            setTitle('');\n            setDescription('');\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-2xl w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Tambah Section\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Judul Section\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: title,\n                                            onChange: (e)=>setTitle(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Masukkan judul section\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Deskripsi \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 font-normal\",\n                                                    children: \"(opsional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: description,\n                                            onChange: (e)=>setDescription(e.target.value),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Masukkan deskripsi section (opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s1(AddSectionModal, \"1UKQWTfo2RWmkwPNsekAdQbvqFk=\");\n_c1 = AddSectionModal;\nfunction AddSubSectionModal(param) {\n    let { isOpen, onClose, onAdd, parentSectionTitle } = param;\n    _s2();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('upload');\n    const [formUrl, setFormUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (title.trim() && (type === 'upload' || type === 'form' && formUrl.trim())) {\n            onAdd(title.trim(), description.trim(), type, type === 'form' ? formUrl.trim() : undefined);\n            setTitle('');\n            setDescription('');\n            setType('upload');\n            setFormUrl('');\n            onClose();\n        }\n    };\n    const handleClose = ()=>{\n        setTitle('');\n        setDescription('');\n        setType('upload');\n        setFormUrl('');\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-2xl w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Tambah Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-2\",\n                            children: [\n                                \"Untuk section: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: parentSectionTitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 28\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Judul Sub-Section\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: title,\n                                            onChange: (e)=>setTitle(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Masukkan judul sub-section\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                \"Deskripsi \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 font-normal\",\n                                                    children: \"(opsional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: description,\n                                            onChange: (e)=>setDescription(e.target.value),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Masukkan deskripsi sub-section (opsional)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Tipe Sub-Section\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"subsectionType\",\n                                                            value: \"upload\",\n                                                            checked: type === 'upload',\n                                                            onChange: (e)=>setType(e.target.value),\n                                                            className: \"mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 bg-blue-100 text-blue-600 rounded-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: \"Upload Dokumen\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Sub-section untuk upload file dokumen\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"subsectionType\",\n                                                            value: \"form\",\n                                                            checked: type === 'form',\n                                                            onChange: (e)=>setType(e.target.value),\n                                                            className: \"mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 bg-green-100 text-green-600 rounded-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: \"Isi Formulir\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Sub-section untuk mengisi formulir online\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this),\n                                type === 'form' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"URL Formulir\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            value: formUrl,\n                                            onChange: (e)=>setFormUrl(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Contoh: /forms/risk-assessment\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Masukkan URL relatif atau absolut untuk formulir\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleClose,\n                                    className: \"flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: \"Batal\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !title.trim() || type === 'form' && !formUrl.trim(),\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-gotham font-medium\",\n                                    children: \"Tambah Sub-Section\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n            lineNumber: 330,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n        lineNumber: 329,\n        columnNumber: 5\n    }, this);\n}\n_s2(AddSubSectionModal, \"j4muNlo4jrHMXOkSO9GEWhR03Ec=\");\n_c2 = AddSubSectionModal;\nfunction EvidenceMappingPage() {\n    var _SUB_DOCUMENTS_find;\n    _s3();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { dynamicSections: dynamicMainSections, dynamicSubSections, updateDynamicSections: updateDynamicMainSections, updateDynamicSubSections } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument)('doc3');\n    const { riskAssessments, updateRiskAssessments } = (0,_contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramRiskAssessments)();\n    const [expandedSection, setExpandedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedSubSection, setExpandedSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFileManager, setShowFileManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasSubmittedForm, setHasSubmittedForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddSection, setShowAddSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddSubSection, setShowAddSubSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSubDocId, setCurrentSubDocId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Check if THIS specific user has submitted risk assessment form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EvidenceMappingPage.useEffect\": ()=>{\n            if (user && user.role === 'scoopers') {\n                try {\n                    const savedAssessments = JSON.parse(localStorage.getItem('risk-assessments') || '[]');\n                    // Check specifically for this user's submission by exact username match\n                    const userSubmission = savedAssessments.find({\n                        \"EvidenceMappingPage.useEffect.userSubmission\": (assessment)=>{\n                            var _assessment_dibuatOleh;\n                            return assessment.submittedBy === user.name || ((_assessment_dibuatOleh = assessment.dibuatOleh) === null || _assessment_dibuatOleh === void 0 ? void 0 : _assessment_dibuatOleh.nama) === user.name;\n                        }\n                    }[\"EvidenceMappingPage.useEffect.userSubmission\"]);\n                    setHasSubmittedForm(!!userSubmission);\n                    console.log(\"Checking form submission for user: \".concat(user.name, \", found: \").concat(!!userSubmission));\n                } catch (error) {\n                    console.error('Error checking form submission:', error);\n                    setHasSubmittedForm(false);\n                }\n            } else {\n                // Non-scoopers always show regular button\n                setHasSubmittedForm(false);\n            }\n        }\n    }[\"EvidenceMappingPage.useEffect\"], [\n        user\n    ]);\n    if (!user) return null;\n    const handleSectionToggle = (sectionId)=>{\n        setExpandedSection(expandedSection === sectionId ? null : sectionId);\n    };\n    const handleSubSectionToggle = (subSectionId)=>{\n        setExpandedSubSection(expandedSubSection === subSectionId ? null : subSectionId);\n    };\n    const handleNavigateToForm = (formUrl)=>{\n        // Navigate to form page\n        window.location.href = formUrl;\n    };\n    const hasFileInSection = (sectionId)=>{\n        const savedFiles = localStorage.getItem('doc-3-section-files');\n        const sectionFiles = savedFiles ? JSON.parse(savedFiles) : {};\n        const filesForSection = sectionFiles[sectionId] || [];\n        return filesForSection.length > 0;\n    };\n    const handleUpload = (subSectionId)=>{\n        setCurrentSection(subSectionId);\n        setShowFileManager(true);\n    };\n    const handleViewFile = (subSectionId)=>{\n        // Handle view file\n        console.log('View file for:', subSectionId);\n        alert(\"View file untuk \".concat(subSectionId));\n    };\n    const handleBack = ()=>{\n        window.history.back();\n    };\n    const getSectionTitle = (sectionId)=>{\n        const sectionTitles = {\n            'klausul-4': 'Klausul 4 - Konteks Organisasi',\n            'klausul-5': 'Klausul 5 - Kepemimpinan',\n            'klausul-6': 'Klausul 6 - Perencanaan'\n        };\n        return sectionTitles[sectionId] || sectionId;\n    };\n    const handleAddSection = (title, description)=>{\n        // In a real app, this would make an API call\n        console.log('Adding new section:', {\n            title,\n            description\n        });\n        alert('Section \"'.concat(title, '\" berhasil ditambahkan!'));\n    };\n    const handleAddSubSection = (title, description, type, formUrl)=>{\n        // In a real app, this would make an API call\n        console.log('Adding new sub-section:', {\n            title,\n            description,\n            type,\n            formUrl,\n            parentId: currentSubDocId\n        });\n        alert('Sub-section \"'.concat(title, '\" (').concat(type === 'form' ? 'Form' : 'Upload', \") berhasil ditambahkan!\"));\n    };\n    const handleOpenAddSection = ()=>{\n        setShowAddSection(true);\n    };\n    const handleOpenAddSubSection = (subDocId, subDocTitle)=>{\n        setCurrentSubDocId(subDocId);\n        setShowAddSubSection(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-red-100 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"font-gotham-rounded text-3xl font-bold text-gray-900\",\n                                                    children: \"Evidence Mapping Klausul\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-gotham text-gray-600 mt-1\",\n                                                    children: \"Pemetaan evidence untuk setiap klausul (khusus Scoopers)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-500 font-gotham mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/dashboard\",\n                                            className: \"hover:text-red-600 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"›\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900\",\n                                            children: \"Evidence Mapping Klausul\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Kembali\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: SUB_DOCUMENTS.map((subDoc, index)=>{\n                                const IconComponent = subDoc.icon;\n                                const isExpanded = expandedSection === subDoc.id;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 cursor-pointer hover:bg-gray-50 transition-all duration-200 \".concat(subDoc.hoverColor),\n                                            onClick: ()=>handleSectionToggle(subDoc.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-xl \".concat(subDoc.color),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-gotham-rounded text-lg font-semibold text-gray-900 mb-2\",\n                                                                        children: [\n                                                                            String.fromCharCode(65 + index),\n                                                                            \". \",\n                                                                            subDoc.title\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-gotham text-gray-600 text-sm leading-relaxed\",\n                                                                        children: subDoc.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 border border-gray-300 rounded-lg transition-all duration-200 \".concat(subDoc.hoverColor),\n                                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-600 transition-colors duration-200 \".concat(subDoc.iconHoverColor)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 19\n                                        }, this),\n                                        isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 bg-gray-50 p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-gotham-rounded text-base font-semibold text-gray-900 mb-1\",\n                                                                    children: [\n                                                                        String.fromCharCode(65 + index),\n                                                                        \".1 Upload Dokumen \",\n                                                                        subDoc.title\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-gotham text-gray-600 text-sm\",\n                                                                    children: [\n                                                                        \"Upload file dokumen untuk \",\n                                                                        subDoc.title\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 ml-4\",\n                                                            children: [\n                                                                (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleUpload(subDoc.id),\n                                                                    className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-gotham font-medium\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewFile(subDoc.id),\n                                                                    className: \"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-sm font-gotham font-medium \".concat(hasFileInSection(subDoc.id) ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-600 text-white hover:bg-gray-700'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: hasFileInSection(subDoc.id) ? 'Lihat File' : 'Belum Ada File'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, subDoc.id, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, this),\n                        (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleOpenAddSection,\n                                    className: \"flex items-center justify-center space-x-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-gotham font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tambah Section Baru\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-gotham text-sm text-gray-500 mt-2\",\n                                    children: \"Tambahkan section baru untuk Evidence Mapping Klausul\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 bg-blue-50 rounded-xl p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowLeftIcon_ChevronDownIcon_ChevronUpIcon_CloudArrowUpIcon_DocumentIcon_DocumentTextIcon_EyeIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-lg font-semibold text-blue-900 mb-2\",\n                                                children: \"Informasi Evidence Mapping\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-blue-800 text-sm leading-relaxed\",\n                                                children: \"Evidence Mapping Klausul khusus untuk tim Scoopers dalam melakukan pemetaan bukti untuk setiap klausul ISO 37001. Dokumen ini mencakup evidence mapping untuk proses recruitment dengan fokus pada klausul konteks organisasi, kepemimpinan, dan perencanaan.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                lineNumber: 577,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileManagerModal, {\n                isOpen: showFileManager,\n                onClose: ()=>setShowFileManager(false),\n                sectionId: currentSection,\n                sectionTitle: getSectionTitle(currentSection)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                lineNumber: 743,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSectionModal, {\n                isOpen: showAddSection,\n                onClose: ()=>setShowAddSection(false),\n                onAdd: handleAddSection\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                lineNumber: 751,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddSubSectionModal, {\n                isOpen: showAddSubSection,\n                onClose: ()=>setShowAddSubSection(false),\n                onAdd: handleAddSubSection,\n                parentSectionTitle: ((_SUB_DOCUMENTS_find = SUB_DOCUMENTS.find((doc)=>doc.id === currentSubDocId)) === null || _SUB_DOCUMENTS_find === void 0 ? void 0 : _SUB_DOCUMENTS_find.title) || ''\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n                lineNumber: 758,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/webmagang/src/app/dokumen/doc-3/page.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, this);\n}\n_s3(EvidenceMappingPage, \"okZJwmfITLI0E3s5x/ETMh6+kJU=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramDocument,\n        _contexts_ProgramContext__WEBPACK_IMPORTED_MODULE_3__.useProgramRiskAssessments\n    ];\n});\n_c3 = EvidenceMappingPage;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"FileManagerModal\");\n$RefreshReg$(_c1, \"AddSectionModal\");\n$RefreshReg$(_c2, \"AddSubSectionModal\");\n$RefreshReg$(_c3, \"EvidenceMappingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dokumen/doc-3/page.tsx\n"));

/***/ })

});